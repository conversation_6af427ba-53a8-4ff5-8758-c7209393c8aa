#%% md
# TimeGPT Symptom Prediction Analysis

This notebook uses TimeGPT to analyze user symptom tracking data and predict future symptoms based on historical patterns and environmental factors.

## Dataset Information
- **Source**: Synthetic tracking history with user symptom data
- **Target**: Various symptoms (cough, wheezing, runny_nose, stuffy_nose, loss_of_smell, sneezing)
- **Features**: Pollen levels (tree, grass, weed), air quality index, environmental factors
- **Goal**: Predict future symptom severity for individual users using TimeGPT
- **Users**: 20 unique users with varying amounts of historical data

#%%
# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# TimeGPT imports
from nixtla import NixtlaClient

# Set plotting style
plt.style.use('default')
sns.set_palette('husl')

print("✅ Libraries imported successfully!")
print("📊 Ready for TimeGPT symptom prediction analysis")

#%% md
## 1. Data Loading and Exploration

#%%
# Load the synthetic tracking history dataset
df = pd.read_csv('synthetic_tracking_history.csv')

print(f"✅ Dataset loaded successfully!")
print(f"📊 Total dataset shape: {df.shape}")
print(f"🏷️  Columns: {list(df.columns)}")

# Filter for user data only (rows with user_id)
user_data = df[df['user_id'].notna()].copy()
print(f"\n👥 User data shape: {user_data.shape}")
print(f"📅 Date range: {user_data['date'].min()} to {user_data['date'].max()}")
print(f"👤 Unique users: {user_data['user_id'].nunique()}")

# Display first few rows
user_data.head()

#%%
# Analyze user data distribution
print("👥 User Data Distribution:")
user_counts = user_data['user_id'].value_counts()
print(user_counts)

print(f"\n📊 Data points per user:")
print(f"   Average: {user_counts.mean():.1f}")
print(f"   Median: {user_counts.median():.1f}")
print(f"   Min: {user_counts.min()}")
print(f"   Max: {user_counts.max()}")

# Visualize user data distribution
plt.figure(figsize=(12, 6))
user_counts.plot(kind='bar', color='skyblue', alpha=0.8)
plt.title('Number of Data Points per User', fontsize=14, fontweight='bold')
plt.xlabel('User ID')
plt.ylabel('Number of Records')
plt.xticks(rotation=45)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

#%%
# Analyze symptom data
symptom_cols = ['cough', 'wheezing', 'runny_nose', 'stuffy_nose', 'loss_of_smell', 'sneezing']
environmental_cols = ['tree_pollen', 'grass_pollen', 'weed_pollen', 'total_pollen', 'us_aqi']

print("🏥 Symptom Data Analysis:")
print(user_data[symptom_cols].describe())

print("\n🌿 Environmental Data Analysis:")
print(user_data[environmental_cols].describe())

#%%
# Visualize symptom distributions
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

for i, symptom in enumerate(symptom_cols):
    axes[i].hist(user_data[symptom].dropna(), bins=20, alpha=0.7, color=sns.color_palette('husl')[i], edgecolor='black')
    axes[i].set_title(f'Distribution of {symptom.replace("_", " ").title()}')
    axes[i].set_xlabel('Severity Score')
    axes[i].set_ylabel('Frequency')
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

#%%
# Analyze correlations between environmental factors and symptoms
correlation_data = user_data[environmental_cols + symptom_cols].corr()

plt.figure(figsize=(12, 10))
sns.heatmap(correlation_data, annot=True, cmap='RdBu_r', center=0, 
            square=True, linewidths=0.5, cbar_kws={"shrink": .8})
plt.title('Correlation Matrix: Environmental Factors vs Symptoms', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

#%% md
## 2. Data Preprocessing for TimeGPT

#%%
# Convert date column to datetime
user_data['date'] = pd.to_datetime(user_data['date'])

# Sort by user and date
user_data = user_data.sort_values(['user_id', 'date']).reset_index(drop=True)

print("📅 Data preprocessing completed")
print(f"📊 Processed data shape: {user_data.shape}")
print(f"📅 Date range: {user_data['date'].min()} to {user_data['date'].max()}")

# Check for missing values in key columns
print("\n❌ Missing values in key columns:")
key_cols = ['date', 'user_id'] + symptom_cols + environmental_cols
missing_data = user_data[key_cols].isnull().sum()
print(missing_data[missing_data > 0])

#%%
# Select users with sufficient data for time series analysis
min_observations = 50  # Minimum number of observations for reliable forecasting
users_with_sufficient_data = user_counts[user_counts >= min_observations].index.tolist()

print(f"👥 Users with sufficient data (>= {min_observations} observations): {len(users_with_sufficient_data)}")
print(f"📋 Selected users: {users_with_sufficient_data}")

# Filter data for selected users
analysis_data = user_data[user_data['user_id'].isin(users_with_sufficient_data)].copy()
print(f"📊 Analysis dataset shape: {analysis_data.shape}")

#%% md
## 3. Individual User Analysis and Forecasting

#%%
# Function to prepare TimeGPT format for a specific user and symptom
def prepare_timegpt_data(user_id, symptom, include_exogenous=True):
    """
    Prepare data in TimeGPT format for a specific user and symptom
    """
    user_subset = analysis_data[analysis_data['user_id'] == user_id].copy()
    
    # Create base TimeGPT dataframe
    timegpt_df = pd.DataFrame({
        'ds': user_subset['date'],
        'y': user_subset[symptom]
    })
    
    # Add exogenous variables if requested
    if include_exogenous:
        for col in environmental_cols:
            timegpt_df[col] = user_subset[col].values
    
    # Remove rows with missing target values
    timegpt_df = timegpt_df.dropna(subset=['y']).reset_index(drop=True)
    
    return timegpt_df

# Test the function with a sample user
sample_user = users_with_sufficient_data[0]
sample_symptom = 'cough'
sample_data = prepare_timegpt_data(sample_user, sample_symptom)

print(f"📊 Sample TimeGPT data for {sample_user} - {sample_symptom}:")
print(f"   Shape: {sample_data.shape}")
print(f"   Date range: {sample_data['ds'].min()} to {sample_data['ds'].max()}")
print("\n📋 Sample data:")
print(sample_data.head())

#%%
# Visualize time series for sample user
fig, axes = plt.subplots(2, 1, figsize=(15, 10))

# Plot symptom over time
axes[0].plot(sample_data['ds'], sample_data['y'], color='#2E86AB', linewidth=2, alpha=0.8, marker='o', markersize=3)
axes[0].set_title(f'{sample_symptom.replace("_", " ").title()} Over Time - {sample_user}', fontsize=14, fontweight='bold')
axes[0].set_ylabel('Symptom Severity')
axes[0].grid(True, alpha=0.3)

# Plot total pollen over time
axes[1].plot(sample_data['ds'], sample_data['total_pollen'], color='#F18F01', linewidth=2, alpha=0.8)
axes[1].set_title(f'Total Pollen Over Time - {sample_user}', fontsize=14, fontweight='bold')
axes[1].set_ylabel('Total Pollen Count')
axes[1].set_xlabel('Date')
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

#%% md
## 4. TimeGPT Setup and Configuration

#%%
# Initialize TimeGPT client
print("🔧 Setting up TimeGPT client...")

try:
    # Use the API key from the original notebook
    nixtla_client = NixtlaClient(api_key="nixak-YraI1EJA3b5jUCVbHDm1lj9FzPjdYyS9UsZqtYHSWwEnll7HkPEih3KmnRJypgpKkeanguIKRUwrjkom")
    print("✅ TimeGPT client initialized!")
    
    # Validate API key
    nixtla_client.validate_api_key()
    print("✅ API key is valid!")
    api_available = True
    
except Exception as e:
    print(f"❌ TimeGPT initialization failed: {e}")
    print("\n🔑 To use TimeGPT, you need to:")
    print("   1. Sign up at https://dashboard.nixtla.io/")
    print("   2. Get your API key")
    print("   3. Set environment variable: export NIXTLA_API_KEY='your_key'")
    print("   4. Or pass directly: NixtlaClient(api_key='your_key')")
    print("\n⚠️  Cannot proceed with TimeGPT forecasting without valid API key")
    nixtla_client = None
    api_available = False

#%% md
## 5. Multi-User Symptom Forecasting

#%%
# Function to perform forecasting for a user-symptom combination
def forecast_user_symptom(user_id, symptom, forecast_days=30, train_ratio=0.8):
    """
    Perform TimeGPT forecasting for a specific user and symptom
    """
    if not api_available:
        return None, None, None
    
    try:
        # Prepare data
        timegpt_data = prepare_timegpt_data(user_id, symptom, include_exogenous=True)
        
        if len(timegpt_data) < 20:  # Need minimum data for forecasting
            print(f"⚠️  Insufficient data for {user_id} - {symptom}: {len(timegpt_data)} observations")
            return None, None, None
        
        # Split data
        split_point = int(len(timegpt_data) * train_ratio)
        train_data = timegpt_data[:split_point][['ds', 'y']].copy()
        test_data = timegpt_data[split_point:].copy()
        
        if len(test_data) == 0:
            forecast_horizon = min(forecast_days, 14)  # Default forecast horizon
            test_data = None
        else:
            forecast_horizon = len(test_data)
        
        # Generate forecast
        forecast = nixtla_client.forecast(
            df=train_data,
            h=forecast_horizon,
            freq='D',
            time_col='ds',
            target_col='y',
            level=[80, 95]
        )
        
        return train_data, test_data, forecast
        
    except Exception as e:
        print(f"❌ Forecasting failed for {user_id} - {symptom}: {e}")
        return None, None, None

# Test forecasting with sample user and symptom
if api_available:
    print(f"🚀 Testing forecasting for {sample_user} - {sample_symptom}...")
    train_data, test_data, forecast = forecast_user_symptom(sample_user, sample_symptom)
    
    if forecast is not None:
        print("✅ Forecasting successful!")
        print(f"📊 Forecast shape: {forecast.shape}")
        print("\n📋 Forecast preview:")
        print(forecast.head())
    else:
        print("❌ Forecasting failed")
else:
    print("⚠️  Skipping forecasting test - TimeGPT API not available")

#%% md
## 6. Forecast Visualization and Analysis

#%%
# Function to visualize forecasting results
def visualize_forecast(user_id, symptom, train_data, test_data, forecast):
    """
    Visualize forecasting results for a user-symptom combination
    """
    plt.figure(figsize=(16, 8))

    # Plot training data
    plt.plot(train_data['ds'], train_data['y'], label='Training Data',
             color='#2E86AB', linewidth=2, alpha=0.8, marker='o', markersize=3)

    # Plot actual test data if available
    if test_data is not None and len(test_data) > 0:
        plt.plot(test_data['ds'], test_data['y'], label='Actual Test Data',
                 color='#F18F01', linewidth=2, alpha=0.8, marker='s', markersize=4)

    # Plot forecast
    plt.plot(forecast['ds'], forecast['TimeGPT'],
             label='TimeGPT Forecast', color='#C73E1D', linewidth=2, linestyle='--', marker='^', markersize=4)

    # Plot confidence intervals
    if 'TimeGPT-lo-95' in forecast.columns:
        plt.fill_between(forecast['ds'],
                        forecast['TimeGPT-lo-95'],
                        forecast['TimeGPT-hi-95'],
                        alpha=0.2, color='#C73E1D', label='95% Confidence Interval')

    if 'TimeGPT-lo-80' in forecast.columns:
        plt.fill_between(forecast['ds'],
                        forecast['TimeGPT-lo-80'],
                        forecast['TimeGPT-hi-80'],
                        alpha=0.3, color='#C73E1D', label='80% Confidence Interval')

    plt.axvline(x=train_data['ds'].iloc[-1], color='gray', linestyle=':', alpha=0.7, label='Forecast Start')
    plt.title(f'🔮 TimeGPT {symptom.replace("_", " ").title()} Prediction - {user_id}', fontsize=16, fontweight='bold')
    plt.xlabel('Date')
    plt.ylabel(f'{symptom.replace("_", " ").title()} Severity')
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# Visualize the sample forecast if available
if api_available and forecast is not None:
    visualize_forecast(sample_user, sample_symptom, train_data, test_data, forecast)

#%%
# Function to calculate forecast accuracy metrics
def calculate_metrics(actual, predicted):
    """
    Calculate various accuracy metrics for forecasting
    """
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

    mae = mean_absolute_error(actual, predicted)
    mse = mean_squared_error(actual, predicted)
    rmse = np.sqrt(mse)
    r2 = r2_score(actual, predicted)
    mape = np.mean(np.abs((actual - predicted) / (actual + 1e-8))) * 100  # Add small epsilon to avoid division by zero

    return {
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'MAPE': mape
    }

# Calculate metrics for sample forecast if test data is available
if api_available and forecast is not None and test_data is not None and len(test_data) > 0:
    actual_values = test_data['y'].values
    forecast_values = forecast['TimeGPT'].values[:len(actual_values)]

    metrics = calculate_metrics(actual_values, forecast_values)

    print(f"📊 Forecast Accuracy Metrics for {sample_user} - {sample_symptom}:")
    for metric, value in metrics.items():
        print(f"   {metric}: {value:.3f}")

#%% md
## 7. Comprehensive Multi-User Analysis

#%%
# Perform forecasting for multiple users and symptoms
def comprehensive_analysis(selected_users=None, selected_symptoms=None, max_users=5):
    """
    Perform comprehensive forecasting analysis across multiple users and symptoms
    """
    if not api_available:
        print("⚠️  TimeGPT API not available - skipping comprehensive analysis")
        return {}

    if selected_users is None:
        selected_users = users_with_sufficient_data[:max_users]

    if selected_symptoms is None:
        selected_symptoms = ['cough', 'runny_nose', 'sneezing']  # Focus on key symptoms

    results = {}

    print(f"🚀 Starting comprehensive analysis for {len(selected_users)} users and {len(selected_symptoms)} symptoms...")

    for user_id in selected_users:
        results[user_id] = {}
        print(f"\n👤 Processing user: {user_id}")

        for symptom in selected_symptoms:
            print(f"   🏥 Forecasting {symptom}...")

            train_data, test_data, forecast = forecast_user_symptom(user_id, symptom, forecast_days=14)

            if forecast is not None:
                results[user_id][symptom] = {
                    'train_data': train_data,
                    'test_data': test_data,
                    'forecast': forecast,
                    'success': True
                }

                # Calculate metrics if test data available
                if test_data is not None and len(test_data) > 0:
                    actual = test_data['y'].values
                    predicted = forecast['TimeGPT'].values[:len(actual)]
                    results[user_id][symptom]['metrics'] = calculate_metrics(actual, predicted)

                print(f"      ✅ Success - forecast generated")
            else:
                results[user_id][symptom] = {'success': False}
                print(f"      ❌ Failed")

    return results

# Run comprehensive analysis
analysis_results = comprehensive_analysis(max_users=3)  # Start with 3 users for testing

#%%
# Visualize results from comprehensive analysis
def visualize_comprehensive_results(results):
    """
    Create visualizations for comprehensive analysis results
    """
    successful_forecasts = []

    for user_id, user_results in results.items():
        for symptom, result in user_results.items():
            if result.get('success', False):
                successful_forecasts.append({
                    'user_id': user_id,
                    'symptom': symptom,
                    'train_size': len(result['train_data']),
                    'forecast_size': len(result['forecast'])
                })

                # Add metrics if available
                if 'metrics' in result:
                    for metric, value in result['metrics'].items():
                        successful_forecasts[-1][metric] = value

    if not successful_forecasts:
        print("⚠️  No successful forecasts to visualize")
        return

    results_df = pd.DataFrame(successful_forecasts)

    # Plot forecast accuracy by user and symptom
    if 'MAE' in results_df.columns:
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))

        # MAE by user
        mae_by_user = results_df.groupby('user_id')['MAE'].mean()
        axes[0].bar(mae_by_user.index, mae_by_user.values, color='skyblue', alpha=0.8)
        axes[0].set_title('Average MAE by User', fontsize=14, fontweight='bold')
        axes[0].set_ylabel('Mean Absolute Error')
        axes[0].tick_params(axis='x', rotation=45)
        axes[0].grid(True, alpha=0.3)

        # MAE by symptom
        mae_by_symptom = results_df.groupby('symptom')['MAE'].mean()
        axes[1].bar(mae_by_symptom.index, mae_by_symptom.values, color='lightcoral', alpha=0.8)
        axes[1].set_title('Average MAE by Symptom', fontsize=14, fontweight='bold')
        axes[1].set_ylabel('Mean Absolute Error')
        axes[1].tick_params(axis='x', rotation=45)
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    print(f"📊 Comprehensive Analysis Summary:")
    print(f"   Total successful forecasts: {len(successful_forecasts)}")
    print(f"   Users analyzed: {results_df['user_id'].nunique()}")
    print(f"   Symptoms analyzed: {results_df['symptom'].nunique()}")

    if 'MAE' in results_df.columns:
        print(f"   Average MAE: {results_df['MAE'].mean():.3f}")
        print(f"   Average R²: {results_df['R2'].mean():.3f}")

# Visualize comprehensive results
visualize_comprehensive_results(analysis_results)

#%% md
## 8. Environmental Factor Impact Analysis

#%%
# Analyze the relationship between environmental factors and symptom predictions
def analyze_environmental_impact(results):
    """
    Analyze how environmental factors correlate with symptom predictions
    """
    if not results:
        print("⚠️  No results available for environmental impact analysis")
        return

    print("🌿 Environmental Factor Impact Analysis")

    # Collect all forecast data with environmental factors
    all_forecasts = []

    for user_id, user_results in results.items():
        for symptom, result in user_results.items():
            if result.get('success', False):
                # Get the original data with environmental factors
                user_env_data = prepare_timegpt_data(user_id, symptom, include_exogenous=True)

                # Add user and symptom info
                user_env_data['user_id'] = user_id
                user_env_data['symptom'] = symptom

                all_forecasts.append(user_env_data)

    if not all_forecasts:
        print("⚠️  No forecast data available for environmental analysis")
        return

    # Combine all data
    combined_data = pd.concat(all_forecasts, ignore_index=True)

    # Calculate correlations between environmental factors and symptoms
    env_cols = ['tree_pollen', 'grass_pollen', 'weed_pollen', 'total_pollen', 'us_aqi']

    correlations = {}
    for symptom in combined_data['symptom'].unique():
        symptom_data = combined_data[combined_data['symptom'] == symptom]
        corr_values = []

        for env_factor in env_cols:
            corr = symptom_data['y'].corr(symptom_data[env_factor])
            corr_values.append(corr)

        correlations[symptom] = corr_values

    # Create correlation heatmap
    corr_df = pd.DataFrame(correlations, index=env_cols)

    plt.figure(figsize=(10, 6))
    sns.heatmap(corr_df, annot=True, cmap='RdBu_r', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title('Environmental Factors vs Symptom Severity Correlations', fontsize=14, fontweight='bold')
    plt.xlabel('Symptoms')
    plt.ylabel('Environmental Factors')
    plt.tight_layout()
    plt.show()

    return corr_df

# Perform environmental impact analysis
env_correlations = analyze_environmental_impact(analysis_results)

#%%
# Create individual user symptom profiles
def create_user_profiles(results, analysis_data):
    """
    Create detailed profiles for each analyzed user
    """
    print("👤 User Symptom Profiles")

    for user_id, user_results in results.items():
        if not any(result.get('success', False) for result in user_results.values()):
            continue

        print(f"\n📋 Profile for {user_id}:")

        # Get user's basic info
        user_info = analysis_data[analysis_data['user_id'] == user_id].iloc[0]
        print(f"   Name: {user_info['name']}")
        print(f"   Age: {user_info['age']}")
        print(f"   Data points: {len(analysis_data[analysis_data['user_id'] == user_id])}")

        # Analyze symptom patterns
        user_data = analysis_data[analysis_data['user_id'] == user_id]

        print(f"   📊 Average symptom levels:")
        for symptom in symptom_cols:
            avg_level = user_data[symptom].mean()
            print(f"      {symptom.replace('_', ' ').title()}: {avg_level:.1f}")

        # Show forecast success rate
        successful_forecasts = sum(1 for result in user_results.values() if result.get('success', False))
        total_attempts = len(user_results)
        success_rate = (successful_forecasts / total_attempts) * 100
        print(f"   🎯 Forecast success rate: {success_rate:.1f}% ({successful_forecasts}/{total_attempts})")

# Create user profiles
create_user_profiles(analysis_results, analysis_data)

#%% md
## 9. Predictive Insights and Recommendations

#%%
# Generate predictive insights
def generate_insights(results, env_correlations):
    """
    Generate actionable insights from the analysis
    """
    print("🔍 Predictive Insights and Recommendations")

    if env_correlations is not None:
        print("\n🌿 Environmental Factor Insights:")

        # Find strongest correlations
        for symptom in env_correlations.columns:
            max_corr_factor = env_correlations[symptom].abs().idxmax()
            max_corr_value = env_correlations[symptom][max_corr_factor]

            print(f"   {symptom.replace('_', ' ').title()}:")
            print(f"      Strongest correlation: {max_corr_factor} (r={max_corr_value:.3f})")

            if abs(max_corr_value) > 0.3:
                direction = "increases" if max_corr_value > 0 else "decreases"
                print(f"      💡 Insight: {symptom.replace('_', ' ').title()} {direction} with {max_corr_factor}")

    print("\n📈 Forecasting Performance Insights:")

    # Analyze forecast performance
    successful_forecasts = []
    for user_id, user_results in results.items():
        for symptom, result in user_results.items():
            if result.get('success', False) and 'metrics' in result:
                successful_forecasts.append({
                    'user_id': user_id,
                    'symptom': symptom,
                    'mae': result['metrics']['MAE'],
                    'r2': result['metrics']['R2']
                })

    if successful_forecasts:
        perf_df = pd.DataFrame(successful_forecasts)

        best_user = perf_df.groupby('user_id')['r2'].mean().idxmax()
        best_symptom = perf_df.groupby('symptom')['r2'].mean().idxmax()

        print(f"   🏆 Best performing user: {best_user}")
        print(f"   🏆 Most predictable symptom: {best_symptom}")
        print(f"   📊 Average R² score: {perf_df['r2'].mean():.3f}")

    print("\n💡 Recommendations:")
    print("   1. Focus monitoring on high-correlation environmental factors")
    print("   2. Implement early warning systems based on pollen forecasts")
    print("   3. Personalize treatment plans based on individual symptom patterns")
    print("   4. Consider seasonal adjustments for symptom management")
    print("   5. Use TimeGPT predictions for proactive healthcare planning")

# Generate insights
generate_insights(analysis_results, env_correlations)

#%% md
## 10. Summary and Next Steps

This notebook demonstrates a comprehensive TimeGPT-based analysis for predicting user symptoms based on historical data and environmental factors:

### ✅ What We Accomplished:
1. **Data Analysis**: Loaded and analyzed synthetic tracking history with 20 users and 6 symptom types
2. **Time Series Preparation**: Converted user symptom data into TimeGPT-compatible format
3. **Multi-User Forecasting**: Generated predictions for multiple users and symptoms
4. **Environmental Correlation**: Analyzed relationships between pollen/air quality and symptoms
5. **Performance Evaluation**: Calculated accuracy metrics and identified best-performing models
6. **Actionable Insights**: Generated recommendations for healthcare applications

### 📊 Key Findings:
- Successfully forecasted symptoms for users with sufficient historical data
- Identified correlations between environmental factors and symptom severity
- Demonstrated personalized prediction capabilities for individual users
- Showed varying predictability across different symptom types

### 🚀 Next Steps:
1. **Expand Analysis**: Include more users and longer time horizons
2. **Feature Engineering**: Add seasonal patterns, weather data, and medication tracking
3. **Model Optimization**: Experiment with different TimeGPT parameters and exogenous variables
4. **Real-time Integration**: Implement live forecasting with current environmental data
5. **Clinical Validation**: Validate predictions with healthcare professionals
6. **Mobile App Integration**: Deploy predictions in user-facing applications
7. **Alert Systems**: Create automated notifications for high-risk periods

### 🔧 Technical Improvements:
- Implement cross-validation for more robust performance evaluation
- Add ensemble methods combining multiple symptom predictions
- Include uncertainty quantification in clinical recommendations
- Optimize for different forecast horizons (daily, weekly, monthly)

This analysis provides a foundation for personalized healthcare applications using TimeGPT's powerful forecasting capabilities.
