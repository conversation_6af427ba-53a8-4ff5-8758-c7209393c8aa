[project]
name = "timegpt"
version = "0.1.0"
description = "TimeGPT Sleep Health Analysis"
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "nixtla>=0.5.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.25.0",
    "plotly>=5.15.0",
    "scikit-learn>=1.3.0",
    "openpyxl>=3.1.0",
    "kagglehub (>=0.3.12,<0.4.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
